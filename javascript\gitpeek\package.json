{"name": "github-repo-analyzer", "version": "1.0.0", "description": "A CLI tool to analyze GitHub repositories", "main": "index.js", "type": "module", "bin": {"repo-analyzer": "./index.js"}, "scripts": {"start": "node index.js"}, "keywords": ["github", "analysis", "cli"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "chalk": "^5.3.0", "commander": "^11.1.0", "conf": "^12.0.0", "cli-table3": "^0.6.3", "inquirer": "^9.2.12", "ora": "^7.0.1", "chart.js": "^3.9.1", "chartjs-node-canvas": "^4.1.6", "json2csv": "^6.0.0-alpha.2", "fs-extra": "^11.1.1", "dotenv": "^16.3.1"}}