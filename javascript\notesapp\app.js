const chalk = require('chalk');
const yargs = require('yargs');
const notes = require('./notes.js');


//command to add note
yargs.command({
    command: 'add',
    describe: 'adds a new note',
    builder:{
        title:{
            describe: 'adds a title to your note!!',
            demandOption: true,
            type: 'string'
        },
        body:{
            describe: 'adds a body to your note!!',
            demandOption: true,
            type: 'string'
        }
    },
    handler(argv){
        notes.addNote(argv.title, argv.body);
    }
});

//command to remove note
yargs.command({ 
    command: 'remove',
    describe: 'removes a note',
    builder:{
        title:{
            describe: 'removes the note with the entered title',
            demandOption: true,
            type: 'string'
        }
    },
    handler(argv){
        notes.removeNote(argv.title);
    }
});

//command to list notes
yargs.command({
    command: 'list',
    describe: 'lists all notes',
    handler(){
        notes.listNotes();
    }
});

//command to read notes
yargs.command({
    command: 'read',
    describe: 'lets you read notes', 
    handler(argv){
        notes.readNotes(argv.title);
    }
});


yargs.parse(); 