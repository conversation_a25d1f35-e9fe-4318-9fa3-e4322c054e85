const fs = require('fs');
const chalk = require('chalk');



const getNotes = function(){

    return 'Your Notes...';
}



const loadNotes = () => {

    try{
        const dataBuffer = fs.readFileSync('notes.json');
        const dataJSON = dataBuffer.toString();
        return JSON.parse(dataJSON);

    } catch(e) {
        return [];
    }
}



const saveNotes = (notes) => {

    const dataJSON = JSON.stringify(notes);
    fs.writeFileSync('notes.json', dataJSON); 
}



const addNote = (title, body) => {

    const notes = loadNotes();
    const duplicateNotes = notes.find((note) => note.title === title);

    if (!duplicateNotes) {

        notes.push({
            title: title,
            body: body
        });

        saveNotes(notes);
        console.log(chalk.green.inverse('note added successfully'));

    } else { 
        console.log(chalk.red.inverse('note title taken'));
    }
} 



const removeNote = (title) => {
    
    const notes = loadNotes();
    const notesToKeep = notes.filter((note) => note.title !== title);

    if (notes.length > notesToKeep.length) {

        saveNotes(notesToKeep);
        console.log(chalk.green.inverse('note removed successfully'));

    } else {
        console.log(chalk.red.inverse('No note found'));
    }
}



const listNotes = () => {

    const notes = loadNotes();
    console.log(chalk.green('your notes: \n'));

    notes.forEach((note) => {
        console.log(note.title);
    })

}



const readNotes = (title) => {
    
    const notes = loadNotes();
    const note = notes.find((note) => note.title === title);

    if (note) {

        console.log(chalk.inverse(note.title));
        console.log(note.body);

    } else {
        console.log(chalk.red.inverse('no note found'));
    }
}

module.exports = {
    getNotes: getNotes,
    addNote: addNote,
    loadNotes: loadNotes,
    removeNote: removeNote,
    listNotes: listNotes,
    readNotes: readNotes
}; 