// const yargs = require('yargs')

// console.log(process.argv)

// const fs = require('fs')

// const dataBuffer = fs.readFileSync('json.json')
// const data = dataBuffer.toString()
// const user = JSON.parse(data)

// user.name = "susu"
// user.age = "16"

// const userJSON = JSON.stringify(user)
// fs.writeFileSync('json.json', userJSON) 

// i = 0

// while (i < 5){
//     console.log('hi')
//     i++
// }


// const event = {

//     name: 'birthday party',
//     guestList: ['sujal', 'lolo', 'yara', 'arnav'],

//     printGuestList() {
//         console.log('guest list for ' + this.name);

//         this.guestList.forEach((guest) => {
//             console.log(guest + ' is attending ' + this.name);
//         });
//     }
// }

// event.printGuestList(); 



//
// Goal: Create method to get incomplete tasks
//
// 1. Define getTasksToDo method
// 2. Use filter to to return just the incompleted tasks (arrow function)
// 3. Test your work by running the script

const tasks = {

    tasks: [{
        text: 'Grocery shopping',
        completed: true
    },{
        text: 'Clean yard',
        completed: false
    }, {
        text: 'Film course',
        completed: false
    }],

    getTasksToDo() {
        const tasksToDo = this.tasks.filter((task) => {
            return task.completed === false
        })
        return tasksToDo
    }
}

console.log(tasks.getTasksToDo())