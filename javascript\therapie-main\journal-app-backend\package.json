{"name": "journal-app-backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "groq-sdk": "^0.21.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.13.2", "openai": "^4.98.0"}, "description": ""}