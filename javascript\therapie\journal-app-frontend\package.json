{"name": "journal-app-frontend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@fvilers/disable-react-devtools": "^1.3.0", "axios": "^1.8.4", "cors": "^2.8.5", "framer-motion": "^12.7.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.0", "recharts": "^2.15.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}