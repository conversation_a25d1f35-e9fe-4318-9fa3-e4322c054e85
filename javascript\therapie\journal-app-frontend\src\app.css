:root {
  --primary-color: #FF8BA7;
  --secondary-color: #FFC6C7;
  --tertiary-color: #FFE5D9;
  --background-color: #FAFAFA;
  --card-color: #FFFFFF;
  --text-color: #33272A;
  --text-light: #594A4F;
  --accent-color: #C3F0CA;
  --error-color: #FF5C5C;

  --happy-color: #C3F0CA;
  --calm-color: #9BF6FF;
  --sad-color: #BDB2FF;
  --anxious-color: #FFD8BE;
  --excited-color: #FDFFB6;
  --tired-color: #CEDDFF;
  --grateful-color: #B5EAD7;
  --frustrated-color: #FFAFCC;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #e97990;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

.auth-card {
  background-color: var(--card-color);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 450px;
  padding: 2.5rem;
  text-align: center;
}

.pie-logo {
  width: 80px;
  height: 80px;
  background-color: var(--tertiary-color);
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  position: relative;
  overflow: hidden;
}

.pie-logo.small {
  width: 40px;
  height: 40px;
  margin: 0 0.5rem 0 0;
  display: inline-block;
  vertical-align: middle;
}

.pie-slice {
  position: absolute;
  width: 50%;
  height: 50%;
  background-color: var(--primary-color);
  top: 0;
  left: 0;
  transform-origin: bottom right;
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}

h2 {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.subtitle {
  color: var(--text-light);
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

input, textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #f0f0f0;
  border-radius: 15px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 15px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  width: 100%;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: var(--text-color);
  border: none;
  border-radius: 15px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.auth-switch {
  margin-top: 1.5rem;
  color: var(--text-light);
}

.error-message {
  background-color: rgba(255, 92, 92, 0.1);
  color: var(--error-color);
  padding: 0.8rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.navbar {
  background-color: var(--card-color);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  padding: 0.8rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.nav-logo a {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.3rem;
  color: var(--text-color);
}

.nav-links {
  display: flex;
  align-items: center;
}

.nav-links a {
  margin-right: 1.5rem;
  color: var(--text-light);
  transition: color 0.3s ease;
}

.nav-links a:hover, .nav-links a.active {
  color: var(--primary-color);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-light);
  font-size: 1rem;
  transition: color 0.3s ease;
}

.logout-btn:hover {
  color: var(--primary-color);
}

.nav-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin-bottom: 5px;
  border-radius: 3px;
}

.dashboard {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.btn-add {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-add a {
  color: white;
}

.journal-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.journal-card {
  background-color: var(--card-color);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.journal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.journal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px 0 0 4px;
}


.journal-date {
  font-size: 0.8rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.journal-title {
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
}

.journal-excerpt {
  color: var(--text-light);
  margin-bottom: 1rem;
  line-height: 1.5;
  height: 4.5em;
  overflow: hidden;
}

.journal-mood {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.mood-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.journal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.journal-actions button {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  transition: color 0.3s ease;
}

.journal-actions button:hover {
  color: var(--error-color);
}

.entry-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.entry-card {
  background-color: var(--card-color);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.mood-selector label {
  display: block;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.mood-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.mood-button {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f0f0f0;
  color: var(--text-color);
}

.mood-button.selected {
  background-color: var(--primary-color);
  color: white;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.insights-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

.insights-card {
  background-color: var(--card-color);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.insights-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.insight-item {
  text-align: center;
  background-color: var(--tertiary-color);
  padding: 1.5rem;
  border-radius: 15px;
}

.insight-value {
  font-size: 2rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.dominant-mood {
  color: var(--primary-color);
}

.mood-chart {
  margin-top: 2rem;
}

.mood-chart h3 {
  margin-bottom: 1.5rem;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-light);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: var(--text-color);
  z-index: 2;
  position: relative;
}

.loading-pie {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 4px solid var(--tertiary-color);
  border-top-color: var(--primary-color); 
  margin-bottom: 1.5rem;
}

.loading p {
  font-size: 1.2rem;
}

.mood-happy::before { background-color: var(--happy-color); }
.mood-calm::before { background-color: var(--calm-color); }
.mood-sad::before { background-color: var(--sad-color); }
.mood-anxious::before { background-color: var(--anxious-color); }
.mood-excited::before { background-color: var(--excited-color); }
.mood-tired::before { background-color: var(--tired-color); }
.mood-grateful::before { background-color: var(--grateful-color); }
.mood-frustrated::before { background-color: var(--frustrated-color); }
.mood-neutral::before { background-color: var(--text-light); }

@media (max-width: 768px) {
  .nav-menu-toggle {
    display: flex;
  }
  
  .nav-links {
    position: fixed;
    top: 60px;
    right: -100%;
    flex-direction: column;
    background-color: var(--card-color);
    width: 70%;
    height: 100vh;
    padding: 2rem;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
  }
  
  .nav-links.active {
    right: 0;
  }
  
  .nav-links a {
    margin: 1rem 0;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
  
  .mood-options {
    justify-content: center;
  }
}

@keyframes pieSliceRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pieGrow {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

.pie-logo .pie-slice {
  animation: pieSliceRotate 15s linear infinite;
}

.loading::after {
  display: none; 
}

.journal-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: var(--tertiary-color);
  transform: translate(50%, -50%);
  opacity: 0.7;
}

.insights-card::before {
  content: '';
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--tertiary-color);
  opacity: 0.3;
  z-index: -1;
}

.btn-primary, .btn-secondary, .btn-add {
  position: relative;
  overflow: hidden;
}

.btn-primary::after, .btn-secondary::after, .btn-add::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  top: -10px;
  right: -10px;
}

.home-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: var(--background-color);
  background-image: radial-gradient(var(--tertiary-color) 10%, transparent 10%);
  background-size: 20px 20px;
}

.home-content {
  max-width: 1000px;
  text-align: center;
  padding: 2rem;
}

.large-pie-logo {
  width: 120px;
  height: 120px;
  background-color: var(--tertiary-color);
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  position: relative;
  overflow: hidden;
}

.home-content h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.tagline {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 3rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-item {
  background-color: var(--card-color);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  margin-bottom: 0.8rem;
}

.feature-item p {
  color: var(--text-light);
}

.home-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  .home-content h1 {
    font-size: 2.5rem;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
  
  .home-actions {
    flex-direction: column;
  }
  
  .home-actions button {
    width: 100%;
    margin-bottom: 1rem;
  }
}

:root {
  --color-primary: #a5d8ff;
  --color-primary-light: #d0ebff;
  --color-secondary: #ffd6e0;
  --color-secondary-light: #ffe9ec;
  --color-success: #c3fae8;
  --color-warning: #fff3bf;
  --color-danger: #ffc9c9;
  --color-text: #495057;
  --color-text-light: #adb5bd;
  --color-background: #f8f9fa;
  --color-card: #ffffff;
  --border-radius: 16px;
  --border-radius-lg: 24px;
  --border-radius-sm: 12px;
  --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

body {
  background-color: var(--color-background);
  overflow-x: hidden;
}

.floating-pie {
  filter: blur(6px);
}

.journal-container {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
  padding: 3rem 1.5rem;
  min-height: 90vh;
  z-index: 1;
}

.journal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  z-index: 2;
  position: relative;
}

.journal-header h1 {
  font-size: 2.5rem;
  color: var(--color-text);
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.btn-new-entry {
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: var(--shadow);
}

.btn-new-entry span {
  font-size: 1.3rem;
  line-height: 1;
}

.tabs-container {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
  z-index: 2;
  position: relative;
}

.tab-button {
  background: var(--color-primary-light);
  color: var(--color-text);
  border: none;
  border-radius: 50px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.tab-button.active {
  background: var(--color-primary);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.entries-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  z-index: 2;
  position: relative;
}

.entry-card {
  background: var(--color-card);
  border-radius: var(--border-radius);
  padding: 1.8rem;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.entry-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.entry-card:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, var(--color-primary), var(--color-secondary));
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.entry-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.2rem;
}

.entry-header h3 {
  margin: 0;
  font-size: 1.4rem;
  color: var(--color-text);
  font-weight: 600;
}

.entry-date {
  font-size: 0.9rem;
  color: var(--color-text-light);
  background: var(--color-background);
  padding: 4px 12px;
  border-radius: 20px;
}

.entry-mood {
  margin-bottom: 1.2rem;
}

.mood-tag {
  display: inline-block;
  padding: 6px 16px;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
}

.mood-happy {
  background-color: #c3fae8;
  color: #0b7261;
}

.mood-calm {
  background-color: #d0ebff;
  color: #1864ab;
}

.mood-sad {
  background-color: #dee2e6;
  color: #495057;
}

.mood-anxious {
  background-color: #ffd6e0;
  color: #a61e4d;
}

.mood-excited {
  background-color: #fff3bf;
  color: #e67700;
}

.mood-tired {
  background-color: #e5dbff;
  color: #5f3dc4;
}

.mood-grateful {
  background-color: #c3fae8;
  color: #087f5b;
}

.mood-frustrated {
  background-color: #ffc9c9;
  color: #c92a2a;
}

.entry-content {
  font-size: 1.1rem;
  color: var(--color-text);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
}

.entry-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-action {
  background: transparent;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: 8px 16px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-action.edit {
  color: #1864ab;
  background-color: #d0ebff;
}

.btn-action.edit:hover {
  background-color: #a5d8ff;
}

.btn-action.delete {
  color: #c92a2a;
  background-color: #ffc9c9;
}

.btn-action.delete:hover {
  background-color: #ffa8a8;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--color-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  z-index: 2;
  position: relative;
  margin-top: 2rem;
}

.empty-state p {
  color: var(--color-text);
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.btn-create-first {
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: var(--shadow);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: var(--color-text);
  z-index: 2;
  position: relative;
}

.loading-pie {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 4px solid var(--color-primary-light);
  border-top-color: var(--color-primary);
  margin-bottom: 1.5rem;
}

.loading p {
  font-size: 1.2rem;
}

.mood-analysis {
  background: var(--color-card);
  border-radius: var(--border-radius);
  padding: 2.5rem;
  box-shadow: var(--shadow);
  z-index: 2;
  position: relative;
}

.analysis-content h3 {
  font-size: 1.8rem;
  color: var(--color-text);
  margin-top: 0;
  margin-bottom: 0.5rem;
  text-align: center;
}

.analysis-content p {
  text-align: center;
  color: var(--color-text-light);
  margin-bottom: 2.5rem;
}

.analysis-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.analysis-section {
  background: var(--color-background);
  border-radius: var(--border-radius-sm);
  padding: 1.5rem;
}

.analysis-section h4 {
  font-size: 1.2rem;
  color: var(--color-text);
  margin-top: 0;
  margin-bottom: 1.2rem;
  text-align: center;
}

.placeholder-chart {
  height: 180px;
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-secondary-light));
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart {
  border-radius: 50%;
  width: 180px;
  height: 180px;
  margin: 0 auto;
}

.chart-message {
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-text);
  opacity: 0.6;
  padding: 0 1rem;
  text-align: center;
}

@media (max-width: 768px) {
  .journal-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .tabs-container {
    width: 100%;
  }
  
  .tab-button {
    flex: 1;
    text-align: center;
  }
  
  .analysis-sections {
    grid-template-columns: 1fr;
  }
}

.btn-analyze {
  background-color: #6c5ce7;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-analyze:hover {
  background-color: #5649c0;
}

.btn-analyze:disabled {
  background-color: #a29bfe;
  cursor: not-allowed;
}

.analysis-container {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 15px;
  border-left: 4px solid #6c5ce7;
}

.analysis-container h3 {
  color: #2d3436;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.analysis-content {
  color: #2d3436;
  line-height: 1.6;
}

.analysis-content p {
  margin-bottom: 0.8rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
  padding: 1.5rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(108, 92, 231, 0.2);
  border-radius: 50%;
  border-top-color: #6c5ce7;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.analysis-content p:first-child {
  font-weight: 500;
  padding: 12px 15px;
  background-color: rgba(108, 92, 231, 0.1);
  border-radius: 8px;
  margin-bottom: 1.2rem;
  border-left: 3px solid #6c5ce7;
} 