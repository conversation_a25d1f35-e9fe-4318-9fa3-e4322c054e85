import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON>rowserRouter } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import "./styles.css";
import App from "./routes/App";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <Toaster position="top-right" toastOptions={{ style: { borderRadius: '12px' } }} />
      <App />
    </BrowserRouter>
  </React.StrictMode>
);

