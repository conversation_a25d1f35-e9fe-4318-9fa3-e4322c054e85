@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: light;
}

@layer base {
  body {
    @apply bg-base-50 text-slate-800 antialiased;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-2xl shadow-soft border border-base-200;
  }
  .btn {
    @apply inline-flex items-center gap-2 rounded-xl px-4 py-2 transition-colors;
  }
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-400;
  }
  .btn-soft {
    @apply btn bg-base-100 hover:bg-base-200;
  }
  .input {
    @apply w-full rounded-xl border border-base-200 bg-white px-3 py-2 outline-none focus:ring-2 focus:ring-primary-300;
  }
}

