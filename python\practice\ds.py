class Node:

    def __init__(self, value):
        self.value = value
        self.next = None 

class LinkedList:

    def __init__(self):
        self.head = None
        self.tail = None
        self.length = 0


    def __str__(self):
        tempNode = self.head
        result = ''
        
        while tempNode:
            result += str(tempNode.value)

            if tempNode.next:
                result += ' -> '
            tempNode = tempNode.next

        return result  


    def append(self, value):
        newNode = Node(value)

        if not self.head:
            self.head = newNode
            self.tail = newNode 
        else:
            self.tail.next = newNode
            self.tail = newNode

        self.length += 1

    
    def prepend(self, value):
        newNode = Node(value)

        if not self.head:
            self.head = newNode
            self.tail = newNode 
        else:
            newNode.next = self.head
            self.head = newNode
        
        self.length += 1


    def insert(self, value, index):
        newNode = Node(value)
        tempNode = self.head

        if index < 0 or index > self.length:
            return False
        
        if not self.head:
            self.head = newNode
            self.tail = newNode 
        else:
            if index == 0:
                newNode.next = self.head
                self.head = newNode
            else:    
                for _ in range(index - 1):
                    tempNode = tempNode.next 
                
                newNode.next = tempNode.next 
                tempNode.next = newNode

        self.length += 1


    def traverse(self):
        current = self.head
        
        while current:
            print(current.value)
            current = current.next

    
    def search(self, target):
        current = self.head
        
        while current:

            if current.value == target:
                return True

            current = current.next
        
        return False


    def get(self, index):
        current = self.head
        
        if index == -1:
            return self.tail
        
        if index < -1 or index >= self.length:
            return None 
        
        for _ in range(index):
            current = current.next
        
        return current


    def setValue(self, index, value):
        temp = self.get(index)
        
        if temp:
            temp.value = value
            return True

        return False


    def popFirst(self):
        poppedNode = self.head

        if not self.head:
            return None 
        
        if self.length == 1:
            self.head = None
            self.tail = None

        
        self.head = self.head.next
        poppedNode.next = None
        
        self.length -= 1

        return poppedNode


    def popLast(self):
        poppedNode = self.tail
        temp = self.head

        if not self.head:
            return None 
        
        if self.length == 1:
            self.head = None
            self.tail = None


        while temp.next != self.tail:
            temp = temp.next
        
        self.tail = temp
        temp.next = None

        self.length -= 1

        return poppedNode 


    def remove(self, index):
        if index == 0:
            return self.popFirst()
        
        if index < -1 or index >= self.length:
            return None
        
        if index == self.length - 1 or index == -1:
            return self.popLast()
        
        prevNode = self.get(index - 1)
        poppedNode = prevNode,next
        prevNode.next = poppedNode,next
        poppedNode.next = None

        self.length -= 1

    
    def reverse(self):
        prev = None
        current = self.head
        
        while current:
            nextNode = current.next
            current.next = prev
            prev = current
            current = nextNode
        
        self.head = prev


    def delete(self):
        self.head = None
        self.tail = None
        self.length = 0




# newLinkedList = LinkedList()
# newLinkedList.append(10)
# newLinkedList.append(20)
# newLinkedList.append(30)
# newLinkedList.append(40)
# newLinkedList.prepend(5)
# newLinkedList.insert(25, 3)
# newLinkedList.insert(1, 0)
# newLinkedList.setValue(1, 22)


# print(newLinkedList)
# print(newLinkedList.length) 




class Stack:

    def __init__(self):
        self.list = []

    
    def __str__(self):
        values = [str(x) for x in reversed(self.list)]
        return '\n'.join(values)
    

    def isEmpty(self):
        if self.list:
            return False

        return True
    

    def push(self, value):
        self.list.append(value)


    def pop(self):
        if self.isEmpty():
            return 'list is empty'
        
        return self.list.pop()
    

    def peek(self):
        if self.isEmpty():
            return 'list is empty'
        
        return self.list[len(self.list) - 1]


    def delete(self):
        self.list = None





newStack = Stack()

newStack.push(1)
newStack.push(2)
newStack.push(3)
print(newStack)






class StackLimited:

    def __init__(self, maxLimit):
        self.list = []
        self.maxLimit = maxLimit
    

    def __str__(self):
        values = [str(x) for x in reversed(self.list)]
        return '\n'.join(values)
    

    def isEmpty(self):
        if self.list:
            return False

        return True
    
    
    def isFull(self):
        if len(self.list) == self.maxLimit:
            return True

        return False
    

    def push(self, value):
        if self.isFull():
            return 'stack is full'
        
        self.list.append(value)

    
    def pop(self):
        if self.isEmpty():
            return 'list is empty'

        return self.list.pop()


    def peek(self):
        if self.isEmpty():
            return 'list is empty'
        
        return self.list[len(self.list) - 1]


    def delete(self):
        self.list = None    





class Node:

    def __init__(self, value = None):
        self.value = value
        self.next = next
    

class LinkedListStack:

    def __init__(self):
        self.head = None
    

    def __iter__(self):
        curNode = self.head
        while curNode:
            yield curNode
            curNode = curNode.next 


class StackLL:

    def __init__(self):
        self.linkedList = LinkedListStack()

    
    def __str__(self):
        values = [str(x) for x in reversed(self.list)]
        return '\n'.join(values)

    
    def isEmpty(self):
        if self.linkedList.head():
            return False
        
        return True
    

    def push(self, value):
        node = Node(value)
        node.next = self.linkedList.head
        self.linkedList.head = node
    

    def pop(self):
        if self.isEmpty():
            return 'stack is empty'
        
        nodeValue = self.linkedList.head.value
        self.linkedList.head = self.linkedList.head.next
        return nodeValue
    

    def peek(self):
        if self.isEmpty():
            return 'stack is empty'
        
        nodeValue = self.linkedList.head.value
        return nodeValue
    

    def delete(self):
        self.linkedList.head = None 



class Queue:

    def __init__(self):
        self.list = []
    

    def __str__(self):
        values = [str(x) for x in (self.list)]
        return ' '.join(values)
    

    def isEmpty(self):
        if self.list:
            return False
        
        return True
    

    def enqueue(self, value):
        self.list.append(value)
        return
    
    def dequeue(self):
        if self.list:
            return self.list.pop(0)
        
        return 'list is empty' 
    

    def peek(self):
        if self.list:
            return self.list[0]
        
        return 'list is empty' 
    

    def delete(self):
        self.list = None 






class CircularQueue:

    def __init__(self, maxSize):
        self.list = maxSize * [None]
        self.maxSize = maxSize
        self.start = -1
        self.top = -1 
    

    def __str__(self):
        values = [str(x) for x in self.list]
        return ' '.join(values)
    

    def isFull(self):
        if self.top + 1 == self.start:
            return True
        elif self.start == 0 and self.top + 1 == self.maxSize:
            return True
        else:
            return False
    

    def isEmpty(self):
        if self.top == -1:
            return True
        else:
            return False
    

    def enqueue(self, value):
        if self.isFull():
            return 'queue is full'
        else:
            if self.top + 1 == self.maxSize:
                self.top = 0
            else:
                self.top += 1
                if self.start == -1:
                    self.start = 0
            self.list[self.top] = value
            return
    

    def dequeue(self):

        if self.isEmpty():
            return 'queue is empty'
        else:
            firstElement = self.list[self.start]
            start = self.start

            if self.start == self.top:
                self.start = -1
                self.top = -1

            elif self.start + 1 == self.maxSize:
                self.start = 0

            else:
                self.start += 1
            self.list[start] = None

            return firstElement
    

    def peek(self):
        if self.isEmpty():
            return 'queue is empty'
        else:
            return self.list[self.start]
    

    def delete(self):
        self.list = self.maxSize * [None]
        self.top = -1
        self.start = -1 



class TreeNode:

    def __init__(self, data, children = []):
        self.data = data
        self.children = children
    

    def __str__(self, level=0):
        ret = "  " * level + str(self.data)  + "\n"
        for child in self.children:
            ret += child.__str__(level + 1)
        return ret
    

    def addChild(self, TreeNode):
        self.children.append(TreeNode)



from ds import Queue as queue

class TreeNode:

    def __init__(self, data):
        self.data = data
        self.leftChild = None
        self.rightChild = None

newBT = TreeNode("Drinks")
leftChild = TreeNode("Hot")
tea = TreeNode("Tea")
coffee = TreeNode("Coffee")
leftChild.leftChild = tea
leftChild.rightChild = coffee
rightChild = TreeNode("Cold")
newBT.leftChild = leftChild
newBT.rightChild = rightChild

def preOrderTraversal(rootNode):
    if not rootNode:
        return
    print(rootNode.data)
    preOrderTraversal(rootNode.leftChild)
    preOrderTraversal(rootNode.rightChild)

def inOrderTraversal(rootNode):
    if not rootNode:
        return
    inOrderTraversal(rootNode.leftChild)
    print(rootNode.data)
    inOrderTraversal(rootNode.rightChild)

def postOrderTraversal(rootNode):
    if not rootNode:
        return
    postOrderTraversal(rootNode.leftChild)
    postOrderTraversal(rootNode.rightChild)
    print(rootNode.data)

def levelOrderTraversal(rootNode):
    if not rootNode:
        return
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            print(root.value.data)
            if (root.value.leftChild is not None):
                customQueue.enqueue(root.value.leftChild)
            
            if (root.value.rightChild is not None):
                customQueue.enqueue(root.value.rightChild)

def searchBT(rootNode, nodeValue):
    if not rootNode:
        return "The BT does not exist"
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            if root.value.data == nodeValue:
                return "Success"
            if (root.value.leftChild is not None):
                customQueue.enqueue(root.value.leftChild)
            
            if (root.value.rightChild is not None):
                customQueue.enqueue(root.value.rightChild)
        return "Not found"

def insertNodeBT(rootNode, newNode):
    if not rootNode:
        rootNode = newNode
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            if root.value.leftChild is not None:
                customQueue.enqueue(root.value.leftChild)
            else:
                root.value.leftChild = newNode
                return "Successfully Inserted"
            if root.value.rightChild is not None:
                customQueue.enqueue(root.value.rightChild)
            else:
                root.value.rightChild = newNode
                return "Successfully Inserted"

def getDeepestNode(rootNode):
    if not rootNode:
        return
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            if (root.value.leftChild is not None):
                customQueue.enqueue(root.value.leftChild)
            
            if (root.value.rightChild is not None):
                customQueue.enqueue(root.value.rightChild)
        deepestNode = root.value
        return deepestNode

def deleteDeepestNode(rootNode, dNode):
    if not rootNode:
        return
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            if root.value is dNode:
                root.value = None
                return
            if root.value.rightChild:
                if root.value.rightChild is dNode:
                    root.value.rightChild = None
                    return
                else:
                    customQueue.enqueue(root.value.rightChild)
            if root.value.leftChild:
                if root.value.leftChild is dNode:
                    root.value.leftChild = None
                    return
                else:
                    customQueue.enqueue(root.value.leftChild)

def deleteNodeBT(rootNode, node):
    if not rootNode:
        return "The BT does not exist"
    else:
        customQueue = queue.Queue()
        customQueue.enqueue(rootNode)
        while not(customQueue.isEmpty()):
            root = customQueue.dequeue()
            if root.value.data == node:
                dNode = getDeepestNode(rootNode)
                root.value.data = dNode.data
                deleteDeepestNode(rootNode, dNode)
                return "The node has been successfully deleted"
            if (root.value.leftChild is not None):
                customQueue.enqueue(root.value.leftChild)
            
            if (root.value.rightChild is not None):
                customQueue.enqueue(root.value.rightChild)
        return "Failed to delete"

def deleteBT(rootNode):
    rootNode.data = None
    rootNode.leftChild = None
    rootNode.rightChild = None
    return "The BT has been successfully deleted" 

inOrderTraversal(newBT)
