from collections import Counter


def selection_sort(arr):

    for i in range(len(arr)):
        min_index = i
        for j in range(i + 1, len(arr)):
            if arr[j] < arr[min_index]:
                min_index = j
        arr[i], arr[min_index] = arr[min_index], arr[i]



def bubble_sort(arr):
   
    for n in range(len(arr) - 1, 0, -1):
        for i in range(n):
            if arr[i] > arr[i + 1]:
                arr[i], arr[i + 1] = arr[i + 1], arr[i]
               


def insertion_sort(arr):

    if len(arr) <= 1:
        return
 
    for i in range(1, len(arr)):
        min_elem = arr[i]
        j = i-1
        while j >= 0 and arr[j] > min_elem:  
            arr[j+1] = arr[j]
            j -= 1
        arr[j+1] = min_elem



def quicksort(arr, left, right):
    if left < right:
        partition_pos = partition(arr, left, right)
        quicksort(arr, left, partition_pos - 1)
        quicksort(arr, partition_pos + 1, right)



def partition(arr, left, right):
    i = left
    j = right - 1
    pivot = arr[right]
   
    while i < j:  
        while i < right and arr[i] < pivot:
            i += 1
        while j > left and arr[j] >= pivot:
            j -= 1
        if i < j:
            arr[i], arr[j] = arr[j], arr[i]
       
    if arr[i] > arr[right]:
        arr[i], pivot = pivot, arr[i]
       
    return arr



def dupe(arr):
    seen = set()
    key = set()
    for i in arr:
        if i in seen:
            key.add(i)
        else:
            seen.add(i)
    return key



def mergesort(arr):
    if len(arr) > 1:
        left_arr = arr[:len(arr)//2]
        right_arr = arr[len(arr)//2:]

        mergesort(left_arr)
        mergesort(right_arr)

        i = j = k = 0
        
        while i < len(left_arr) and j < len(right_arr):
            if left_arr[i] < right_arr[j]:
                arr[k] = left_arr[i]
                i += 1
            else:
                arr[k] = right_arr[j]
                j += 1
            k += 1
            
        while i < len(left_arr):
            arr[k] = left_arr[i]
            i += 1
            k += 1

        while j < len(right_arr):
            arr[k] = right_arr[j]
            j += 1
            k += 1
    return arr



def sum_of_natural_numbers(n):
    return n * (n + 1) // 2



def factorial(n):

    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)

# recurrence tree for factorial when n = 5
# factorial(5)
#  └── 5 * factorial(4)
#        └── 4 * factorial(3)
#              └── 3 * factorial(2)
#                    └── 2 * factorial(1)
#                          └── 1 (base case)



def count_digits(n):
    count = 0
    while n > 0:
        n //= 10 #trick for eliminating the last digit
        count += 1
    return count



def palindrome(n):
    #return str(n) == str(n)[::-1] 
    original = n
    reversed_num = 0
    while n > 0:
        digit = n % 10 #trick for obtaining the last digit          
        reversed_num = reversed_num * 10 + digit 
        n //= 10                
    
    return original == reversed_num 



import math
def quadraticRoots(a, b, c):
    roots = []
    root1 = 0
    root2 = 0

    determinent = b**2 - (4 * a * c)

    if determinent < 0:
        roots.append(-1)
    else:
        root1 = math.floor((-1 * b + math.sqrt(determinent)) / (2 * a))
        root2 = math.floor((-1 * b - math.sqrt(determinent)) / (2 * a))
        roots.append(max(root1, root2))
        roots.append(min(root1, root2))
    return roots



def trailingZeroes(N):
    count = 0
    while N >= 5:
        N //= 5
        count += N
    return count



def gcd(a, b):
    while b != 0:
        a, b = b, a % b
    return a



def lcm(a, b):
    return abs(a * b) // gcd(a, b)



def is_prime(n):
    if n <= 1:
        return False
    elif n <= 3:
        return True
    
    if n % 2 or n % 3 == 0:
        return False
    
    i = 5

    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    
    return True 



def prime_factors(n):
    factors = []

    while n % 2 == 0:
        factors.append(2)
        n //= 2

    i = 3
    while i * i < n:
        while n % i == 0:
            factors.append(i)
            n //= i
        i += 2
    
    if n > 2:
        factors.append(n)
    
    return factors



def sieve(lim):  #sieve of erathosthenes

    primes = [True] * (lim + 1)  
    p = 2

    while p * p <= lim:
        if primes[p]:
            for i in range(p * p, lim + 1, p):
                primes[i] = False
        p += 1

    prime_list = []
    for p in range(2, lim + 1):
        if primes[p]:
            prime_list.append(p)

    return prime_list



#for a given number n, find the amount of numbers less than or equal to n that have exactly 3 divisors

def threediv(n):
    def isitprime(num):
        if num <= 1:
            return False
        elif num <= 3:
            return True
        
        if num % 2 == 0 or num % 3 == 0:
            return False
        
        i = 5
        while i * i <= num:
            if num % i == 0 or num % (i + 2) == 0:
                return False
            i += 6
        return True
    
    count = 0
    i = 1
    while i * i <= n:
        if isitprime(i):
            count += 1
        i += 1

    return count



def commonprefix(strs: list[str]) -> str:
    if not strs:
        return ""

    prefix = strs[0]

    for indiv in strs[1:]:
        while indiv[:len(prefix)] != prefix:
            prefix = prefix[:-1] 
            if not prefix:
                return ""
    return prefix 



def removeduplicatesinplace(nums):
    if not nums:
        return 0
    
    k = i = 1

    while i < len(nums):
        if nums[i] != nums[i - 1]:
            nums[k] = nums[i]
            k += 1
        i += 1

    return k



def slidingwindow(haystack:str, needle: str) -> int:
    if not needle:
        return -1
    
    hay, need = len(haystack), len(needle)

    if need > hay:
        return -1
    
    for i in range(hay - need + 1):
        if haystack[i: i + need]  == needle:
            return i 
        
    return -1



#Given a sorted array of distinct integers and a target value, return the index if the target is found.
#If not, return the index where it would be if it were inserted in order.

def findsorted(arr, target):
    low, high = 0, len(arr) - 1

    while high >= low:
        mid = (high + low)//2

        if arr[mid] == target:
            return mid

        elif arr[mid] < target:
            low = mid + 1

        else:
            high = mid - 1

    arr.append(target)
    return sorted(arr.index(target))



def temp():

    days = int(input("Enter number of days: "))
    i = 1
    days_above_avg = 0
    temperature_list = []


    while i <= days:
        temperature = int(input(f"Enter day {i}'s temperature: "))

        i += 1
        temperature_list.append(temperature)

    average_temperature = round(sum(temperature_list)/days,2)


    for i in temperature_list:
        if i > average_temperature:
            days_above_avg += 1
    

    print(f"average temperature was: {average_temperature}")


    if days_above_avg == 1:
        print(f"{days_above_avg} day was above average")
    
    else:
        print(f"{days_above_avg} days were above average")



def find_two_largest(arr):
    if len(arr) < 2:
        raise ValueError
    
    
    first = second = float('-inf')
    for i in arr:
        if i > first:
            second = first
            first = i
        elif i > second and i != first:
            second = i
    
    return first, second 



def plusOne(digits: list[int]) -> list[int]:

        n = len(digits)
    
        for i in range(n - 1, -1, -1):      
            digits[i] += 1     
            if digits[i] < 10:
                return digits
            digits[i] = 0
    
        return [1] + digits



def mySqrt(x: int):
    
    if x == 0:
        return 0
    
    low, high = 0, x

    while high >= low:

        mid = (low + high)//2

        if mid * mid == x:
            return mid
        elif mid * mid < x:
            low = mid + 1
        else:
            high = mid - 1
    
    return high 



def diagonal_sum(matrix):
    total = 0
    for i in range(len(matrix)):
        total += matrix[i][i]  
    return total



def climbStairs(n):
    if n <= 2:
        return n
    
    first, second = 1, 2
 
    for i in range(3, n + 1):
        first, second = second, first + second

    return second 



def dnf_sort(arr): #dutch_national_flag

    low, mid, high = 0, 0, len(arr) - 1

    while high >= mid:
        if arr[mid] == 0:
            arr[low], arr[mid] = arr[mid], arr[low]
            low += 1 
            mid += 1
        elif arr[mid] == 1:
            mid += 1
        else:
            arr[high], arr[mid] = arr[mid], arr[high]
            high -= 1
    
    return arr 



def isPalindrome(string: str) -> bool:
    cleaned_string = ''.join(char.lower() for char in string if char.isalnum())

    return cleaned_string == cleaned_string[::-1]



def pascal(numRows):
        triangle = []
        
        for i in range(numRows):
            row = [1] * (i + 1)

            for j in range(1, i):
                row[j] = triangle[i-1][j-1] + triangle[i-1][j]
                
            triangle.append(row)
    
        return triangle



def split(nums):
    
    count = Counter(nums)

    for i in count.values():
        if i > 2:
            return False

    return True  



def max_subarray(arr):  #kadanes algorithm 

    max_sum = float('-inf')
    current_sum = 0

    for num in arr:
        current_sum = max(num, current_sum + num)
        max_sum = max(max_sum, current_sum)

    return max_sum  



def majorityElement(nums) -> int:
    
    candidate = None
    count = 0

    for num in nums:
        if count == 0:  
            candidate = num
        if num == candidate:
            count += 1
        else:
            count -= 1

    return candidate 



from collections import defaultdict

def groupAnagrams(strs):
    
    anagrams = defaultdict(list)

    for s in strs:
        
        sorted_str = ''.join(sorted(s))
        anagrams[sorted_str].append(s)
    
    return list(anagrams.values())



def packet_analysis(packet_ids, k):

    seen = {} 
    problems = {}  

    for i in range(len(packet_ids)):
        
        if packet_ids[i] in seen:
            gap = i - seen[packet_ids[i]]

            if gap <= k:
                if packet_ids[i] not in problems:
                    
                    problems[packet_ids[i]] = {
                        "min_gap": gap,
                        "max_gap": gap
                    }

                else:
                    problems[packet_ids[i]]["min_gap"] = min(problems[packet_ids[i]]["min_gap"], gap)
                    problems[packet_ids[i]]["max_gap"] = max(problems[packet_ids[i]]["max_gap"], gap)
      
        seen[packet_ids[i]] = i

    if not problems:
        return True

    else:
        result = []
        for packet_ids[i], details in problems.items():
            result.append(f"Packet ID {packet_ids[i]}: Minimum gap = {details['min_gap']}, Maximum gap = {details['max_gap']}")
        return "\n".join(result) 
    



def packet_verification(packet_ids, k):

    seen = {}  
    
    for i in range(len(packet_ids)):
        
        if packet_ids[i] in seen:

            if i - seen[packet_ids[i]] <= k:
                return False 
        
        seen[packet_ids[i]] = i

    return True 



def maxProfit(prices):
       
    if len(prices) < 2:
        return 0
    
    min_price = prices[0]  
    max_profit = 0  

    for price in prices:
        
        profit = price - min_price
        max_profit = max(max_profit, profit)
        min_price = min(min_price, price)

    return max_profit



def majorityElement(nums):
    
    candidate = None
    count = 0

    for num in nums:
        if count == 0:  
            candidate = num
        if num == candidate:
            count += 1
        else:
            count -= 1

    return candidate



def warehouse(slots):

    if slots:
        max_slot = max(slot)
    else:
        max_slot = 1

    slot_map = [False] * (max_slot + 1)

    for slot in slots:

        if slot > 0:  
            slot_map[slot] = True

    for i in range(1, len(slot_map)):

        if not slot_map[i]:
            return i  

    return max_slot + 1 
