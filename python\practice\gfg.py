def count_digits(num):  #count digits

    digits = 0 

    while num > 0:
        num //= 10
        digits += 1
    
    return digits 



def palindrome_num(num): #palindrome number
    
    reversed_num = 0

    while num > 0:
        digit = num % 10       
        reversed_num = reversed_num * 10 + digit 
        n //= 10                
    
    return num == reversed_num 



def factorial(num): #factorial of num

    if num  == 0 or num == 1:
        return 1
    
    else:
        return num * factorial(num - 1) 
    


def trailingzeros(num):
    
    count = 0 
    fact = factorial(num)

    while fact % 10 == 0:
        count += 1
        fact //= 10
        
    return count 



def one_to_n(n):

    if n == 0:
        return 
    
    one_to_n(n-1)
    print(n, end = " ")

    return ''



def n_to_one(n):

    if n == 1:
        return 1
    
    print(n, end = ' ')

    return n_to_one(n-1)



def binarysearch(arr, n, low, high):

    while low <= high:
        mid = (low + high)//2

        if arr[mid] == n:
            return mid
        
        elif arr[mid] < n:
            low = mid + 1

        else:
            high = mid - 1
    
    return -1



def infinitearr(arr, target):

    if target == arr[0]:
        return 0
    
    i = 1

    while arr[i] < target:
        i *= 2
    
    if arr[i] == target:
        return i
    
    return binarysearch(arr, target, i // 2 + 1, i - 1)



def sqrtfloor(n):

    low, high = 0, n

    while low <= high:
        mid = (low + high)//2

        if mid * mid == n:
            return mid
        
        elif mid * mid < n:
            low = mid + 1
        
        else:
            high = mid - 1
    
    return high 



def minNumber(arr,low,high):

    while low < high:

        mid = (low + high) // 2

        if arr[mid] < arr[high]:
            high = mid
        
        else:
            low = mid + 1
    
    return arr[low] 