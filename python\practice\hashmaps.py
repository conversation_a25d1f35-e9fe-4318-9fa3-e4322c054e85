def twosums(nums, target):
    hashmap = {}
    for i in range(len(nums)):
        compliment = target - nums[i]
        if compliment in hashmap:
            return [hashmap[compliment], i]
        hashmap[nums[i]] = i 



def romanToInt(s: str) -> int:
    translations = {
        "I": 1,
        "V": 5,
        "X": 10,
        "L": 50,
        "C": 100,
        "D": 500,
        "M": 1000
    }
    number = 0
    s = s.replace("IV", "IIII").replace("IX", "VIIII")
    s = s.replace("XL", "XXXX").replace("XC", "LXXXX")
    s = s.replace("CD", "CCCC").replace("CM", "DCCCC")
    for char in s:
        number += translations[char]
    return number



def dict_comprehension():
    import random

    city_names = ['paris', 'rome', 'london']

    city_temps = {city:random.randint(20,30) for city in city_names}
    print(city_temps)

    hot_cities = {city:temp for (city,temp) in city_temps.items() if temp>25}

    print (hot_cities) 




def merge_dicts(dict1, dict2):
    merged_dict = dict1.copy()  

    for key, value in dict2.items():
        if key in merged_dict:            
            merged_dict[key] += value
        else:          
            merged_dict[key] = value

    return merged_dict 



def max_value_key(my_dict):

    #return max(my_dict, key=my_dict.get)

    max_num = float('-inf')
    max_key = None
    for key, value in my_dict.items():
        if value > max_num:
            max_num = value
            max_key = key
    return max_key



def reverse_dict(my_dict):
    reversed = {}  
    for key, value in my_dict.items():
        reversed[value] = key  
    return reversed



def check_same_frequency(list1, list2):
    
    def count_elements(lst):
        counter = {}
        for element in lst:
            if element in counter:
                counter[element] += 1
            else:
                counter[element] = 1
        return counter
    
    return count_elements(list1) == count_elements(list2)


def removedupes(nums):
    seen = []
    result = []

    for i in nums:
        if i not in seen:
            result.append(i)
        seen.append(i)
    
    return result
