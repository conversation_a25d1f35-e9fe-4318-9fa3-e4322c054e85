from collections import Counter
from collections import defaultdict
from typing import List


#arrays and hashing

def containsDuplicate(nums):
    num_set = set()

    for i in nums:
        if i in num_set:
            return True
        num_set.add(i)

    return False



def validAnagram(s, t):
    return Counter(s) == Counter(t)



def twoSum(nums, target):

    hashmap = {}

    for i in range(len(nums)):
        compliment = target - nums[i]

        if compliment in hashmap:
            return [hashmap[compliment], i]
        
        hashmap[nums[i]] = i 



def groupAnagrams(strs):
        
        anagrams = defaultdict(list)
        result = []

        for s in strs:
           
            sorted_str = tuple(sorted(s))
            anagrams[sorted_str].append(s)
        
        for value in anagrams.values():
            result.append(value)

        return result 



def topKFrequent(nums, k):
       
        count = Counter(nums)
        sorted_keys = sorted(count.keys(), key=lambda key: count[key], reverse=True)

        return sorted_keys[:k] 




def encode(strs):

    return "".join(f"{len(s)}|{s}" for s in strs)



def decode(s):

    decoded_list = []
    i = 0

    while i < len(s):
        j = s.find("|", i)
        length = int(s[i:j])
        i = j + 1
        decoded_list.append(s[i:i+length])
        i += length

    return decoded_list



def productExceptSelf(nums):
    
    total_product = 1
    zeroCount = 0

    for num in nums:
        if num == 0:
            zeroCount += 1
        else:
            total_product *= num

    result = []

    for num in nums:

        if zeroCount == 1:
            if num == 0:
                result.append(total_product)
            else:
                result.append(0)
        
        elif zeroCount > 1:
            result.append(0)
            
        else:
            result.append(total_product // num)

    return result 




def isValidSudoku(board):
    
    for row in board:
        seen = set()

        for num in row:

            if num != '.':
                if num in seen:
                    return False

                seen.add(num)
    
    for col in range(9):
        seen = set()

        for row in range(9):
            num = board[row][col]

            if num != '.':
                if num in seen:
                    return False

                seen.add(num)
    
    for box_row in range(0, 9, 3):

        for box_col in range(0, 9, 3):
            seen = set()

            for row in range(box_row, box_row + 3):

                for col in range(box_col, box_col + 3):
                    num = board[row][col]

                    if num != '.':
                        if num in seen:
                            return False
                            
                        seen.add(num)
    
    return True



def longestConsecutive(nums):

        num_set = set(nums)
        longest = 0

        for num in num_set:

            if num - 1 not in num_set:
                length = 1

                while num + length in num_set:
                    length += 1

                longest = max(longest, length)

        return longest 


#stack

def validParenthesis(s):
        
        stack = []
        matching = {')': '(', '}': '{', ']': '['}

        for bracket in s:

            if bracket in matching:  

                if not stack or stack.pop() != matching[bracket]:
                    return False
            else:  
                stack.append(bracket)
        
        return not stack 



class MinStack:

    def __init__(self):
        self.stack = []  

    def push(self, val: int) -> None:
        self.stack.append(val)

    def pop(self) -> None:
        self.stack.pop()

    def top(self) -> int:
        return self.stack[-1] 

    def getMin(self) -> int:
        return min(self.stack)  



def evalRPN(tokens):
    stack = []

    for token in tokens:

        if token in ['+', '-', '*', '/']:
            b = stack.pop()
            a = stack.pop()

            if token == '+':
                result = a + b
            elif token == '-':
                result = a - b
            elif token == '*':
                result = a * b
            elif token == '/':
                result == int(a / b)
            
            stack.append(result)
        
        else:
            stack.append(int(token))
    
    return stack[0]



def generate_parenthesis(n):
    result = []

    def backtrack(current, open_count, close_count):
        if len(current) == 2 * n:
            result.append(current)
            return

        if open_count < n:
            backtrack(current + '(', open_count + 1, close_count)

        if close_count < open_count:
            backtrack(current + ')', open_count, close_count + 1)

    backtrack('', 0, 0)
    return result
