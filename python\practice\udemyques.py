from random import randint

class Node:

    def __init__(self, value = None):
        self.value = value
        self.next = None
        self.prev = None


    def __str__(self):
        return str(self.value)
    
class LinkedList:

    def __init__(self, value = None):
        self.head = None
        self.tail = None


    def __iter__(self):
        curNode = self.head

        while curNode:
            yield curNode
            curNode = curNode.next

    
    def __str__(self):
        values = [str(x.value) for x in self]
        return ' => '.join(values)
    

    def __len__(self):
        result = 0
        node = self.value

        while node:
            result += 1
            node = node.next
        
        return result
    

    def add(self, value):
        if self.head is None:
            newNode = Node(value)
            self.head = newNode
            self.tail = newNode

        else:
            self.tail.next = Node(value)
            self.tail = self.tail.next
        
        return self.tail
    

    def generate(self, n, minValue, maxValue):
        self.head = None
        self.tail = None

        for i in range(n):
            self.add(randint(minValue, maxValue))
        
        return self


    def nthToLast(ll, n):
        p1 = ll.head
        p2 = ll.head

        for i in range(n):
            if p2 is None:
                return None
            
            p2 = p2.next
        
        while p2:
            p1 = p1.next
            p2 = p2.next
        
        return p1
    

    def partition(ll, x):
        curNode = ll.head
        ll.tail = ll.head

        while curNode:
            nextNode = curNode.next
            curNode.next = None

            if curNode.value < x:
                curNode.next = ll.head
                ll.head = curNode
            
            else:
                ll.tail.next = curNode
                ll.tail = curNode
            curNode = nextNode
        
        if ll.tail.next is not None:
            ll.tail.next = None 


    def sumll(lla, llb):
        n1 = lla.head
        n2 = llb.head
        carry = 0
        ll = LinkedList()

        while n1 or n2:
            result = carry
            if n1:
                result += n1.value
                n1 = n1.next
            if n2:
                result += n2.value
                n2 = n2.next

            ll.add(int(result % 10))
            carry = result / 10
        
        return ll