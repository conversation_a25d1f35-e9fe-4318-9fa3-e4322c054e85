<!DOCTYPE html>
<html lang="en">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Forecast</title>
    <style>

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            transition: background 0.5s ease-in-out;
            color: white;
        }
        body.sunny {
            background: linear-gradient(to bottom, #FFD700, #FFA500); 
        }
        body.rainy {
            background: linear-gradient(to bottom, #3a7bd5, #00d2ff); 
        }
        body.snowy {
            background: linear-gradient(to bottom, #83a4d4, #b6fbff); 
        }
        body.cloudy {
            background: linear-gradient(to bottom, #757f9a, #d7dde8); 
        }
        body.default {
            background: linear-gradient(to right, #ff7e5f, #feb47b); 
        }
        .container {
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            padding: 40px;
            border-radius: 15px;
            width: 100%;
            max-width: 800px;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"] {
            padding: 10px;
            font-size: 1.2em;
            width: 60%;
            margin-right: 10px;
            border-radius: 5px;
            border: none;
            outline: none;
        }
        button {
            padding: 10px 20px;
            font-size: 1.2em;
            background-color: #ff6347;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #ff4500;
        }
        .weather-info {
            margin-top: 20px;
            background-color: rgba(0, 0, 0, 0.6);
            padding: 15px;
            border-radius: 8px;
        }
        .weather-info img {
            width: 80px;
            height: 80px;
        }
        .error {
            color: #ff4040;
        }
    </style>
</head>

<body class="{{ weather_class | default('default') }}">

    <div class="container">
        <h1>Weather Forecast</h1>
        
        <form method="POST">
            <input type="text" name="city" placeholder="Enter city" required>
            <button type="submit">Get Weather</button>
        </form>
        
        {% if error %}
            <p class="error">{{ error }}</p>
        {% endif %}
        
        {% if weather %}
            <div class="weather-info">
                <h2>Weather in {{ city }}</h2>
                <img src="http://openweathermap.org/img/wn/{{ weather['weather'][0]['icon'] }}@2x.png" alt="{{ weather['weather'][0]['description'] }}">
                <p>Temperature: {{ weather['main']['temp'] }}°C</p>
                <p>Weather: {{ weather['weather'][0]['description'] | capitalize }}</p>
                <p>Humidity: {{ weather['main']['humidity'] }}%</p>
                <p>Wind Speed: {{ weather['wind']['speed'] }} m/s</p>
                <p>Pressure: {{ weather['main']['pressure'] }} hPa</p>
            </div>
        {% endif %} 
        
    </div>

</body>
</html> 