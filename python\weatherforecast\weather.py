from flask import Flask, render_template, request
import requests

weather = Flask(__name__)

@weather.route("/", methods=["GET", "POST"])
def front():
    error = None
    city = None
    weather = None
    weather_class = "default"  

    if request.method == "POST":
        city = request.form.get("city")
        
        if not city:
            error = "Please enter a city"
        else:
            api_key = "8e6efec72dd5be9ccf853308b262e821"  
            url = f"http://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}&units=metric"
            response = requests.get(url)
            
            if response.status_code == 200:
                weather = response.json()
                weather_main = weather['weather'][0]['main'].lower()

                if "rain" in weather_main:
                    weather_class = "rainy"
                elif "clear" in weather_main:
                    weather_class = "sunny"
                elif "snow" in weather_main:
                    weather_class = "snowy"
                elif "cloud" in weather_main:
                    weather_class = "cloudy"
                else:
                    weather_class = "default"
            else:
                error = "City not found"

    return render_template("front.html", error=error, weather=weather, city=city, weather_class=weather_class)


if __name__ == "__main__":
    weather.run(debug=True) 