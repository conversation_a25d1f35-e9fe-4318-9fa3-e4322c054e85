<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shri <PERSON><PERSON><PERSON>a Technical Campus</title>
  <style>
    body {
      display: flex;
      margin: 0;
      font-family: Arial, sans-serif;
    }
    .sidebar {
      width: 200px; /* Reduced width */
      background-color: #f4f4f4;
      padding: 20px;
      border-right: 1px solid #ddd;
      height: 100vh;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }
    .sidebar h2 {
      margin: 0 0 10px;
      font-size: 18px;
      color: #333;
    }
    .sidebar ul {
      list-style: none;
      padding: 0;
    }
    .sidebar ul li {
      margin-bottom: 10px;
    }
    .sidebar ul li button {
      background-color: #007BFF;
      color: white;
      border: none;
      padding: 10px;
      border-radius: 5px;
      cursor: pointer;
      width: 100%;
      text-align: left;
    }
    .sidebar ul li button:hover {
      background-color: #0056b3;
    }
    .content {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background-size: contain; /* Ensures the whole image fits */
      background-position: center;
      background-repeat: no-repeat;
      height: 100vh; /* Keeps it full height */
    }

    
    .pdf-list {
      display: none;
      flex-direction: column;
      align-items: flex-start;
      padding: 20px;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }
    .pdf-list a {
      margin-bottom: 10px;
      color: #007BFF;
      text-decoration: none;
    }
    .pdf-list a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <h2>Menu</h2>
    <ul>
      <li><button onclick="showPDFs('semester1')">Semester 1</button></li>
      <li><button onclick="showPDFs('semester2')">Semester 2</button></li>
    </ul>
  </div>
  <div class="content" id="content" style="background-image: url('backgroundreal.png');">
    <div id="semester1" class="pdf-list">
      <!-- Add your Semester 1 PDFs here -->
      <a href="sem 1.pdf" target="_blank">Semester 1 - Result</a>
    </div>
    <div id="semester2" class="pdf-list">
      <!-- Add your Semester 2 PDFs here -->
      <a href="sem 2.pdf" target="_blank">Semester 2 - Result</a>
    </div>
  </div>
  <script>
    function showPDFs(semester) {
      document.querySelectorAll('.pdf-list').forEach(list => list.style.display = 'none');
      document.getElementById(semester).style.display = 'flex';
    }
  </script>
</body>
</html>
